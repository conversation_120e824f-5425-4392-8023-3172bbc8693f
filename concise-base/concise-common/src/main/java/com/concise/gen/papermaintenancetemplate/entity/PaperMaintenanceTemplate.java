package com.concise.gen.papermaintenancetemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseEntity;

import com.concise.gen.papertopic.entity.PaperTopic;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 笔录模板
 *
 * <AUTHOR>
 * @date 2025-08-05 09:23:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("paper_maintenance_template")
public class PaperMaintenanceTemplate extends BaseEntity {

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  量卷题目 */
    private String title;

    /**  量卷类型，字典值：LJLX */
    private String paperType;

    /**  笔录类型，字典值：BLLX */
    private String blType;

    /**  笔录类型名称 */
    private String blTypeName;

    /**  状态，0：启用 1：禁用 */
    private Integer status;

    /**  使用单位 */
    private String jzjg;

    /**  使用单位名称 */
    private String jzjgName;

    /**  分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正 */
    private Integer score;

    /**  删除状态， 0：未删除  1：已删除 */
    private Integer delFlag;

    /**
     * 操作人员
     */
    private String operator;

    @TableField(exist = false)
    private List<PaperTopic> topicList;
}
