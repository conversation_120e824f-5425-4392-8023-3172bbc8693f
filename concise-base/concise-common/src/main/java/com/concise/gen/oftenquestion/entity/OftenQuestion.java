package com.concise.gen.oftenquestion.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.Date;

/**
 * 常用问题导入
 *
 * <AUTHOR>
 * @date 2025-08-05 14:45:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("often_question")
public class OftenQuestion extends BaseEntity {

    /**  id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  题目 */
    private String question;

    /**  指标 */
    private String indexName;

    /**  题目1 */
    private String question1;

    /**  题目2 */
    private String question2;

    /**  题目3 */
    private String question3;

    /**  题目4 */
    private String question4;

    /**  题目5 */
    private String question5;

}
