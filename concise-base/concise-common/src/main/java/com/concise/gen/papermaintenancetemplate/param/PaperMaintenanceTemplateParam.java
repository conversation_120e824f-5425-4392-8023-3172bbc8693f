package com.concise.gen.papermaintenancetemplate.param;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.papertopic.param.PaperTopicParam;

import lombok.Data;

/**
* 笔录模板参数类
 *
 * <AUTHOR>
 * @date 2025-08-05 09:23:48
*/
@Data
public class PaperMaintenanceTemplateParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 量卷题目
     */
    @NotBlank(message = "量卷题目不能为空，请检查title参数", groups = {add.class, edit.class})
    private String title;

    /**
     * 量卷类型，字典值：LJLX
     */
    @NotBlank(message = "量卷类型，字典值：LJLX不能为空，请检查paperType参数", groups = {add.class, edit.class})
    private String paperType;

    /**
     * 笔录类型，字典值：BLLX
     */
    @NotBlank(message = "笔录类型，字典值：BLLX不能为空，请检查blType参数", groups = {add.class, edit.class})
    private String blType;

    /**
     * 笔录类型名称
     */
    @NotBlank(message = "笔录类型名称不能为空，请检查blTypeName参数", groups = {add.class, edit.class})
    private String blTypeName;

    /**
     * 状态，0：启用 1：禁用
     */
    @NotNull(message = "状态，0：启用 1：禁用不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

    /**
     * 使用单位
     */
    @NotBlank(message = "使用单位不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 使用单位名称
     */
    @NotBlank(message = "使用单位名称不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    private String jzjgName;

    /**
     * 分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正
     */
    @NotNull(message = "分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正不能为空，请检查score参数", groups = {add.class, edit.class})
    private Integer score;

    /**
     * 删除状态， 0：未删除  1：已删除
     */
    @NotNull(message = "删除状态， 0：未删除  1：已删除不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    private Integer delFlag;

    /**
     * 量卷题目列表
     */
    private List<PaperTopicParam> topicList;

    /**
     * 操作人员
     */
    private String operator;

}
