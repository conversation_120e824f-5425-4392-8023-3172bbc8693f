package com.concise.gen.papermaintenancetemplate.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.papermaintenance.enums.PaperMaintenanceExceptionEnum;
import com.concise.gen.papermaintenancetemplate.entity.PaperMaintenanceTemplate;
import com.concise.gen.papermaintenancetemplate.enums.PaperMaintenanceTemplateExceptionEnum;
import com.concise.gen.papermaintenancetemplate.mapper.PaperMaintenanceTemplateMapper;
import com.concise.gen.papermaintenancetemplate.param.PaperMaintenanceTemplateParam;
import com.concise.gen.papermaintenancetemplate.service.PaperMaintenanceTemplateService;
import com.concise.gen.papertopic.entity.PaperTopic;
import com.concise.gen.papertopic.param.PaperTopicParam;
import com.concise.gen.papertopic.service.PaperTopicService;
import com.concise.gen.papertopicitem.entity.PaperTopicItem;
import com.concise.gen.papertopicitem.param.PaperTopicItemParam;
import com.concise.gen.papertopicitem.service.PaperTopicItemService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 笔录模板service接口实现类
 *
 * <AUTHOR>
 * @date 2025-08-05 09:23:48
 */
@Service
public class PaperMaintenanceTemplateServiceImpl extends ServiceImpl<PaperMaintenanceTemplateMapper, PaperMaintenanceTemplate> implements PaperMaintenanceTemplateService {

    @Resource
    private PaperTopicService paperTopicService;
    @Resource
    private PaperTopicItemService paperTopicItemService;

    @Override
    public PageResult<PaperMaintenanceTemplate> page(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        QueryWrapper<PaperMaintenanceTemplate> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(paperMaintenanceTemplateParam)) {

            // 根据量卷题目 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getTitle())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getTitle, paperMaintenanceTemplateParam.getTitle());
            }
            // 根据量卷类型，字典值：LJLX 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getPaperType())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getPaperType, paperMaintenanceTemplateParam.getPaperType());
            }
            // 根据笔录类型，字典值：BLLX 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getBlType())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getBlType, paperMaintenanceTemplateParam.getBlType());
            }
            // 根据笔录类型名称 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getBlTypeName())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getBlTypeName, paperMaintenanceTemplateParam.getBlTypeName());
            }
            // 根据状态，0：启用 1：禁用 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getStatus())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getStatus, paperMaintenanceTemplateParam.getStatus());
            }
            // 根据使用单位 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getJzjg())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getJzjg, paperMaintenanceTemplateParam.getJzjg());
            }
            // 根据使用单位名称 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getJzjgName())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getJzjgName, paperMaintenanceTemplateParam.getJzjgName());
            }
            // 根据分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getScore())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getScore, paperMaintenanceTemplateParam.getScore());
            }
            // 根据删除状态， 0：未删除  1：已删除 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getDelFlag())) {
                queryWrapper.lambda().eq(PaperMaintenanceTemplate::getDelFlag, paperMaintenanceTemplateParam.getDelFlag());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PaperMaintenanceTemplate> list(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        return this.list();
    }

    @Override
    public void add(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        // 校验题目列表不能为空
        if (CollectionUtil.isEmpty(paperMaintenanceTemplateParam.getTopicList())) {
            throw new ServiceException(PaperMaintenanceExceptionEnum.TOPIC_LIST_NOT_NULL);
        }

        // 复制并保存量卷维护主表信息
        PaperMaintenanceTemplate paperMaintenance = new PaperMaintenanceTemplate();
        BeanUtil.copyProperties(paperMaintenanceTemplateParam, paperMaintenance);
        paperMaintenance.setCreateTime(DateUtil.date());
        paperMaintenance.setUpdateTime(DateUtil.date());
        paperMaintenance.setOperator(paperMaintenanceTemplateParam.getOperator());
        this.save(paperMaintenance);

        // 获取生成的主键ID
        String paperMaintenanceId = paperMaintenance.getId();

        // 保存题目和选项
        savePaperTopicsAndItems(paperMaintenanceTemplateParam.getTopicList(), paperMaintenanceId);
    }

    /**
     * 保存题目和选项
     *
     * @param topicList          题目列表
     * @param paperMaintenanceId 量卷维护ID
     * <AUTHOR>
     * @date 2025-03-24 09:51:02
     */
    private void savePaperTopicsAndItems(List<PaperTopicParam> topicList, String paperMaintenanceId) {
        if (CollectionUtil.isEmpty(topicList)) {
            return;
        }

        for (int i = 0; i < topicList.size(); i++) {
            PaperTopicParam topicParam = topicList.get(i);
            // 新增时强制ID为null
            topicParam.setId(null);
            topicParam.setPaperMaintenanceId(paperMaintenanceId);
            topicParam.setSerialNumber(i + 1);

            PaperTopic paperTopic = new PaperTopic();
            BeanUtil.copyProperties(topicParam, paperTopic);
            paperTopic.setCreateTime(DateUtil.date());
            //标记为模板题目
            paperTopic.setPopular(1);
            // 只用save
            paperTopicService.save(paperTopic);

            String paperTopicId = paperTopic.getId();
            saveTopicItems(topicParam.getItemList(), paperTopicId);
        }
    }

    /**
     * 保存题目选项
     *
     * @param itemList     选项列表
     * @param paperTopicId 题目ID
     * <AUTHOR>
     * @date 2025-03-24 09:51:02
     */
    private void saveTopicItems(List<PaperTopicItemParam> itemList, String paperTopicId) {
        if (CollectionUtil.isEmpty(itemList)) {
            return;
        }

        for (int i = 0; i < itemList.size(); i++) {
            PaperTopicItemParam itemParam = itemList.get(i);
            // 新增时强制ID为null
            itemParam.setId(null);
            itemParam.setPaperTopicId(paperTopicId);
            itemParam.setSerialNumber(i + 1);

            PaperTopicItem paperTopicItem = new PaperTopicItem();
            BeanUtil.copyProperties(itemParam, paperTopicItem);
            paperTopicItem.setCreateTime(DateUtil.date());
            // 只用save
            paperTopicItemService.save(paperTopicItem);
        }
    }

    /**
     * 更新题目和选项
     *
     * @param topicList          题目列表
     * @param paperMaintenanceId 量卷维护ID
     * <AUTHOR>
     * @date 2025-03-24 09:51:02
     */
    private void updatePaperTopicsAndItems(List<PaperTopicParam> topicList, String paperMaintenanceId) {
        if (CollectionUtil.isEmpty(topicList)) {
            return;
        }

        // 查询当前量卷下的所有题目
        QueryWrapper<PaperTopic> topicQueryWrapper = new QueryWrapper<>();
        topicQueryWrapper.lambda().eq(PaperTopic::getPaperMaintenanceId, paperMaintenanceId).eq(PaperTopic::getPopular, 1);
        List<PaperTopic> existingTopics = paperTopicService.list(topicQueryWrapper);

        // 收集现有题目ID，用于后续判断哪些需要删除
        List<String> existingTopicIds = existingTopics.stream()
                .map(PaperTopic::getId)
                .collect(java.util.stream.Collectors.toList());

        // 收集新提交的题目ID，用于后续判断哪些需要保留
        List<String> newTopicIds = new java.util.ArrayList<>();

        // 处理每个题目，并设置排序号
        for (int i = 0; i < topicList.size(); i++) {
            PaperTopicParam topicParam = topicList.get(i);
            // 设置量卷维护ID
            topicParam.setPaperMaintenanceId(paperMaintenanceId);
            // 设置排序号，确保按列表顺序排序
            topicParam.setSerialNumber(i + 1);

            if (ObjectUtil.isEmpty(topicParam.getId())) {
                // 新增题目
                PaperTopic paperTopic = new PaperTopic();
                BeanUtil.copyProperties(topicParam, paperTopic);
                paperTopic.setPopular(1);
                paperTopicService.save(paperTopic);

                // 保存题目选项
                saveTopicItems(topicParam.getItemList(), paperTopic.getId());
            } else {
                // 更新题目
                newTopicIds.add(topicParam.getId());

                PaperTopic paperTopic = paperTopicService.getById(topicParam.getId());
                if (ObjectUtil.isNotNull(paperTopic)) {
                    BeanUtil.copyProperties(topicParam, paperTopic);
                    paperTopicService.updateById(paperTopic);

                    // 更新题目选项
                    updateTopicItems(topicParam.getItemList(), paperTopic.getId());
                }
            }
        }

        // 删除不再存在的题目
        existingTopicIds.removeAll(newTopicIds);
        if (!existingTopicIds.isEmpty()) {
            // 删除这些题目
            paperTopicService.removeByIds(existingTopicIds);

            // 删除这些题目下的所有选项
            for (String topicId : existingTopicIds) {
                QueryWrapper<PaperTopicItem> itemQueryWrapper = new QueryWrapper<>();
                itemQueryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, topicId);
                paperTopicItemService.remove(itemQueryWrapper);
            }
        }
    }

    /**
     * 更新题目选项
     *
     * @param itemList     选项列表
     * @param paperTopicId 题目ID
     * <AUTHOR>
     * @date 2025-03-24 09:51:02
     */
    private void updateTopicItems(List<PaperTopicItemParam> itemList, String paperTopicId) {
        if (CollectionUtil.isEmpty(itemList)) {
            // 如果选项列表为空，则删除该题目下的所有选项
            QueryWrapper<PaperTopicItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, paperTopicId);
            paperTopicItemService.remove(itemQueryWrapper);
            return;
        }

        // 查询当前题目下的所有选项
        QueryWrapper<PaperTopicItem> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, paperTopicId);
        List<PaperTopicItem> existingItems = paperTopicItemService.list(itemQueryWrapper);

        // 收集现有选项ID，用于后续判断哪些需要删除
        List<String> existingItemIds = existingItems.stream()
                .map(PaperTopicItem::getId)
                .collect(java.util.stream.Collectors.toList());

        // 收集新提交的选项ID，用于后续判断哪些需要保留
        List<String> newItemIds = new java.util.ArrayList<>();

        // 处理每个选项，并设置排序号
        for (int i = 0; i < itemList.size(); i++) {
            PaperTopicItemParam itemParam = itemList.get(i);
            // 设置题目ID
            itemParam.setPaperTopicId(paperTopicId);
            // 设置排序号，确保按列表顺序排序
            itemParam.setSerialNumber(i + 1);

            if (ObjectUtil.isEmpty(itemParam.getId())) {
                // 新增选项
                PaperTopicItem paperTopicItem = new PaperTopicItem();
                BeanUtil.copyProperties(itemParam, paperTopicItem);
                paperTopicItemService.save(paperTopicItem);
            } else {
                // 更新选项
                newItemIds.add(itemParam.getId());

                PaperTopicItem paperTopicItem = paperTopicItemService.getById(itemParam.getId());
                if (ObjectUtil.isNotNull(paperTopicItem)) {
                    BeanUtil.copyProperties(itemParam, paperTopicItem);
                    paperTopicItemService.updateById(paperTopicItem);
                }
            }
        }

        // 删除不再存在的选项
        existingItemIds.removeAll(newItemIds);
        if (!existingItemIds.isEmpty()) {
            paperTopicItemService.removeByIds(existingItemIds);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        String[] ids = paperMaintenanceTemplateParam.getId().split(",");
        for (String id : ids) {
            this.removeById(id);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        // 校验题目列表不能为空
        if (CollectionUtil.isEmpty(paperMaintenanceTemplateParam.getTopicList())) {
            throw new ServiceException(PaperMaintenanceExceptionEnum.TOPIC_LIST_NOT_NULL);
        }

        // 获取并更新量卷维护主表信息
        PaperMaintenanceTemplate paperMaintenanceTemplate = this.queryPaperMaintenanceTemplate(paperMaintenanceTemplateParam);
        BeanUtil.copyProperties(paperMaintenanceTemplateParam, paperMaintenanceTemplate);
        this.updateById(paperMaintenanceTemplate);

        // 获取量卷维护ID
        String paperMaintenanceId = paperMaintenanceTemplate.getId();

        // 处理题目和选项的更新
        updatePaperTopicsAndItems(paperMaintenanceTemplateParam.getTopicList(), paperMaintenanceId);
    }

    @Override
    public PaperMaintenanceTemplate detail(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        return this.queryPaperMaintenanceTemplate(paperMaintenanceTemplateParam);
    }

    /**
     * 获取笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    private PaperMaintenanceTemplate queryPaperMaintenanceTemplate(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        PaperMaintenanceTemplate paperMaintenanceTemplate = this.getById(paperMaintenanceTemplateParam.getId());
        if (ObjectUtil.isNull(paperMaintenanceTemplate)) {
            throw new ServiceException(PaperMaintenanceTemplateExceptionEnum.NOT_EXIST);
        }
        //填充题目和选项
        List<PaperTopic> topicList = paperTopicService.list(
                new QueryWrapper<PaperTopic>().lambda()
                        .eq(PaperTopic::getPaperMaintenanceId, paperMaintenanceTemplate.getId())
        );
        if (CollectionUtil.isNotEmpty(topicList)) {
            topicList.forEach(paperTopic -> {
                paperTopic.setItemList(paperTopicItemService.list(new QueryWrapper<PaperTopicItem>().lambda().eq(PaperTopicItem::getPaperTopicId, paperTopic.getId())));
            });
        }
        paperMaintenanceTemplate.setTopicList(topicList);
        return paperMaintenanceTemplate;
    }

    @Override
    public void saveAsTemplate(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        // 校验题目列表不能为空
        if (CollectionUtil.isEmpty(paperMaintenanceTemplateParam.getTopicList())) {
            throw new ServiceException(PaperMaintenanceExceptionEnum.TOPIC_LIST_NOT_NULL);
        }
        if (ObjectUtil.isNotEmpty(paperMaintenanceTemplateParam.getId())) {
            PaperMaintenanceTemplate template = this.getById(paperMaintenanceTemplateParam.getId());
            if (ObjectUtil.isNotNull(template)) {
                this.edit(paperMaintenanceTemplateParam);
            }
        } else {
            this.add(paperMaintenanceTemplateParam);
        }

    }
}
