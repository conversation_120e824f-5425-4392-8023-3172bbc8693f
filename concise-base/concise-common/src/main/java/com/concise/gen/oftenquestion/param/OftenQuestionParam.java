package com.concise.gen.oftenquestion.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 常用问题导入参数类
 *
 * <AUTHOR>
 * @date 2025-08-05 14:45:33
*/
@Data
public class OftenQuestionParam extends BaseParam {

    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 题目
     */
    @NotBlank(message = "题目不能为空，请检查question参数", groups = {add.class, edit.class})
    private String question;

    /**
     * 指标
     */
    @NotBlank(message = "指标不能为空，请检查indexName参数", groups = {add.class, edit.class})
    private String indexName;

    /**
     * 题目1
     */
    @NotBlank(message = "题目1不能为空，请检查question1参数", groups = {add.class, edit.class})
    private String question1;

    /**
     * 题目2
     */
    @NotBlank(message = "题目2不能为空，请检查question2参数", groups = {add.class, edit.class})
    private String question2;

    /**
     * 题目3
     */
    @NotBlank(message = "题目3不能为空，请检查question3参数", groups = {add.class, edit.class})
    private String question3;

    /**
     * 题目4
     */
    @NotBlank(message = "题目4不能为空，请检查question4参数", groups = {add.class, edit.class})
    private String question4;

    /**
     * 题目5
     */
    @NotBlank(message = "题目5不能为空，请检查question5参数", groups = {add.class, edit.class})
    private String question5;

}
