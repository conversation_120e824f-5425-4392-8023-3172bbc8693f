package com.concise.gen.oftenquestion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.oftenquestion.entity.OftenQuestion;
import com.concise.gen.oftenquestion.enums.OftenQuestionExceptionEnum;
import com.concise.gen.oftenquestion.mapper.OftenQuestionMapper;
import com.concise.gen.oftenquestion.param.OftenQuestionParam;
import com.concise.gen.oftenquestion.service.OftenQuestionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 常用问题导入service接口实现类
 *
 * <AUTHOR>
 * @date 2025-08-05 14:45:33
 */
@Service
public class OftenQuestionServiceImpl extends ServiceImpl<OftenQuestionMapper, OftenQuestion> implements OftenQuestionService {

    @Override
    public PageResult<OftenQuestion> page(OftenQuestionParam oftenQuestionParam) {
        QueryWrapper<OftenQuestion> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(oftenQuestionParam)) {

            // 根据题目 查询
            if (ObjectUtil.isNotEmpty(oftenQuestionParam.getQuestion())) {
                queryWrapper.lambda().eq(OftenQuestion::getQuestion, oftenQuestionParam.getQuestion());
            }
            // 根据指标 查询
            if (ObjectUtil.isNotEmpty(oftenQuestionParam.getIndexName())) {
                queryWrapper.lambda().eq(OftenQuestion::getIndexName, oftenQuestionParam.getIndexName());
            }
            // 根据题目1 查询
            if (ObjectUtil.isNotEmpty(oftenQuestionParam.getQuestion1())) {
                queryWrapper.lambda().eq(OftenQuestion::getQuestion1, oftenQuestionParam.getQuestion1());
            }
            // 根据题目2 查询
            if (ObjectUtil.isNotEmpty(oftenQuestionParam.getQuestion2())) {
                queryWrapper.lambda().eq(OftenQuestion::getQuestion2, oftenQuestionParam.getQuestion2());
            }
            // 根据题目3 查询
            if (ObjectUtil.isNotEmpty(oftenQuestionParam.getQuestion3())) {
                queryWrapper.lambda().eq(OftenQuestion::getQuestion3, oftenQuestionParam.getQuestion3());
            }
            // 根据题目4 查询
            if (ObjectUtil.isNotEmpty(oftenQuestionParam.getQuestion4())) {
                queryWrapper.lambda().eq(OftenQuestion::getQuestion4, oftenQuestionParam.getQuestion4());
            }
            // 根据题目5 查询
            if (ObjectUtil.isNotEmpty(oftenQuestionParam.getQuestion5())) {
                queryWrapper.lambda().eq(OftenQuestion::getQuestion5, oftenQuestionParam.getQuestion5());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<OftenQuestion> list(OftenQuestionParam oftenQuestionParam) {
        return this.list();
    }

    @Override
    public void add(OftenQuestionParam oftenQuestionParam) {
        OftenQuestion oftenQuestion = new OftenQuestion();
        BeanUtil.copyProperties(oftenQuestionParam, oftenQuestion);
        this.save(oftenQuestion);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(OftenQuestionParam oftenQuestionParam) {
        this.removeById(oftenQuestionParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(OftenQuestionParam oftenQuestionParam) {
        OftenQuestion oftenQuestion = this.queryOftenQuestion(oftenQuestionParam);
        BeanUtil.copyProperties(oftenQuestionParam, oftenQuestion);
        this.updateById(oftenQuestion);
    }

    @Override
    public OftenQuestion detail(OftenQuestionParam oftenQuestionParam) {
        return this.queryOftenQuestion(oftenQuestionParam);
    }

    /**
     * 获取常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    private OftenQuestion queryOftenQuestion(OftenQuestionParam oftenQuestionParam) {
        OftenQuestion oftenQuestion = this.getById(oftenQuestionParam.getId());
        if (ObjectUtil.isNull(oftenQuestion)) {
            throw new ServiceException(OftenQuestionExceptionEnum.NOT_EXIST);
        }
        return oftenQuestion;
    }
}
