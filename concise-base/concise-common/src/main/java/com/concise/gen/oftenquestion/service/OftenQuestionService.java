package com.concise.gen.oftenquestion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.oftenquestion.entity.OftenQuestion;
import com.concise.gen.oftenquestion.param.OftenQuestionParam;
import java.util.List;

/**
 * 常用问题导入service接口
 *
 * <AUTHOR>
 * @date 2025-08-05 14:45:33
 */
public interface OftenQuestionService extends IService<OftenQuestion> {

    /**
     * 查询常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    PageResult<OftenQuestion> page(OftenQuestionParam oftenQuestionParam);

    /**
     * 常用问题导入列表
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    List<OftenQuestion> list(OftenQuestionParam oftenQuestionParam);

    /**
     * 添加常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    void add(OftenQuestionParam oftenQuestionParam);

    /**
     * 删除常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    void delete(OftenQuestionParam oftenQuestionParam);

    /**
     * 编辑常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    void edit(OftenQuestionParam oftenQuestionParam);

    /**
     * 查看常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
     OftenQuestion detail(OftenQuestionParam oftenQuestionParam);
}
