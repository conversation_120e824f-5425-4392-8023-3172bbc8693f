package com.concise.gen.papermaintenancetemplate.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.papermaintenancetemplate.entity.PaperMaintenanceTemplate;
import com.concise.gen.papermaintenancetemplate.param.PaperMaintenanceTemplateParam;

/**
 * 笔录模板service接口
 *
 * <AUTHOR>
 * @date 2025-08-05 09:23:48
 */
public interface PaperMaintenanceTemplateService extends IService<PaperMaintenanceTemplate> {

    /**
     * 查询笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    PageResult<PaperMaintenanceTemplate> page(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam);

    /**
     * 笔录模板列表
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    List<PaperMaintenanceTemplate> list(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam);

    /**
     * 添加笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    void add(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam);

    /**
     * 删除笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    void delete(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam);

    /**
     * 编辑笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    void edit(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam);

    /**
     * 查看笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
     PaperMaintenanceTemplate detail(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam);

    /**
     * 量卷保存为常用模板
     *
     * @param paperMaintenanceTemplateParam
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    void saveAsTemplate(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam);
}
