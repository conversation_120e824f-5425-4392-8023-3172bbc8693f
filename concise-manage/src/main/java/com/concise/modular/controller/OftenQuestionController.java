package com.concise.modular.controller;

import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.oftenquestion.entity.OftenQuestion;
import com.concise.gen.oftenquestion.param.OftenQuestionParam;
import com.concise.gen.oftenquestion.service.OftenQuestionService;
import com.concise.gen.papertopic.entity.PaperTopic;
import com.concise.gen.papertopic.service.PaperTopicService;
import com.concise.gen.papertopicitem.entity.PaperTopicItem;
import com.concise.gen.papertopicitem.service.PaperTopicItemService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 常用问题导入控制器
 *
 * <AUTHOR>
 * @date 2025-08-05 14:45:33
 */
@Api(tags = "常用问题导入")
@RestController
public class OftenQuestionController {

    @Resource
    private OftenQuestionService oftenQuestionService;

    @Resource
    private PaperTopicService paperTopicService;

    @Resource
    private PaperTopicItemService paperTopicItemService;

    /**
     * 查询常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    @GetMapping("/oftenQuestion/page")
    @ApiOperation("常用问题导入_分页查询")
    public ResponseData page(OftenQuestionParam oftenQuestionParam) {
        return new SuccessResponseData(oftenQuestionService.page(oftenQuestionParam));
    }

    /**
     * 添加常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    @PostMapping("/oftenQuestion/add")
    @ApiOperation("常用问题导入_增加")
    @BusinessLog(title = "常用问题导入_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(OftenQuestionParam.add.class) OftenQuestionParam oftenQuestionParam) {
        oftenQuestionService.add(oftenQuestionParam);
        return new SuccessResponseData();
    }

    /**
     * 删除常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    @PostMapping("/oftenQuestion/delete")
    @ApiOperation("常用问题导入_删除")
    @BusinessLog(title = "常用问题导入_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(OftenQuestionParam.delete.class) OftenQuestionParam oftenQuestionParam) {
        oftenQuestionService.delete(oftenQuestionParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    @PostMapping("/oftenQuestion/edit")
    @ApiOperation("常用问题导入_编辑")
    @BusinessLog(title = "常用问题导入_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(OftenQuestionParam.edit.class) OftenQuestionParam oftenQuestionParam) {
        oftenQuestionService.edit(oftenQuestionParam);
        return new SuccessResponseData();
    }

    /**
     * 查看常用问题导入
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    @GetMapping("/oftenQuestion/detail")
    @ApiOperation("常用问题导入_查看")
    public ResponseData detail(@Validated(OftenQuestionParam.detail.class) OftenQuestionParam oftenQuestionParam) {
        return new SuccessResponseData(oftenQuestionService.detail(oftenQuestionParam));
    }

    /**
     * 常用问题导入列表
     *
     * <AUTHOR>
     * @date 2025-08-05 14:45:33
     */
    @GetMapping("/oftenQuestion/list")
    @ApiOperation("常用问题导入_列表")
    public ResponseData list(OftenQuestionParam oftenQuestionParam) {
        return new SuccessResponseData(oftenQuestionService.list(oftenQuestionParam));
    }

    /**
     * 批量处理数据
     */
    @GetMapping("/oftenQuestion/batch")
    @ApiOperation("常用问题导入_批量处理数据")
    public ResponseData batch() {
        List<OftenQuestion> list = oftenQuestionService.list();
        if (list.isEmpty()) {
            return new SuccessResponseData();
        }
        for (OftenQuestion oftenQuestion : list) {
            PaperTopic paperTopic = paperTopicService.getOne(new LambdaQueryWrapper<PaperTopic>().eq(PaperTopic::getTopicName, oftenQuestion.getQuestion()).eq(PaperTopic::getIndexName, oftenQuestion.getIndexName()).last("limit 1"));
            if (ObjectUtil.isNotEmpty(paperTopic)) {
                //获取选项
                List<PaperTopicItem> options = paperTopicItemService.list(new LambdaQueryWrapper<PaperTopicItem>().eq(PaperTopicItem::getPaperTopicId, paperTopic.getId()));
                if (ObjectUtil.isNotEmpty(oftenQuestion.getQuestion1())) {
                    createNewTopic(oftenQuestion, paperTopic, options, 1);
                }
                if (ObjectUtil.isNotEmpty(oftenQuestion.getQuestion2())) {
                    createNewTopic(oftenQuestion, paperTopic, options, 2);
                }
                if (ObjectUtil.isNotEmpty(oftenQuestion.getQuestion3())) {
                    createNewTopic(oftenQuestion, paperTopic, options, 3);
                }
                if (ObjectUtil.isNotEmpty(oftenQuestion.getQuestion4())) {
                    createNewTopic(oftenQuestion, paperTopic, options, 4);
                }
                if (ObjectUtil.isNotEmpty(oftenQuestion.getQuestion5())) {
                    createNewTopic(oftenQuestion, paperTopic, options, 5);
                }
            }
        }
        return new SuccessResponseData();
    }

    //创建一个新的题目，保持原来的选项，做关联关系
    public void createNewTopic(OftenQuestion oftenQuestion, PaperTopic paperTopic, List<PaperTopicItem> options, int index) {
        PaperTopic newPaperTopic = new PaperTopic();
        BeanUtils.copyProperties(paperTopic, newPaperTopic);
        newPaperTopic.setId(IdWorker.getIdStr());
        if (index == 1) {
            newPaperTopic.setTopicName(oftenQuestion.getQuestion1());
        }
        if (index == 2) {
            newPaperTopic.setTopicName(oftenQuestion.getQuestion2());
        }
        if (index == 3) {
            newPaperTopic.setTopicName(oftenQuestion.getQuestion3());
        }
        if (index == 4) {
            newPaperTopic.setTopicName(oftenQuestion.getQuestion4());
        }
        if (index == 5) {
            newPaperTopic.setTopicName(oftenQuestion.getQuestion5());
        }
        //如果有同名的题目，则不创建
        if (paperTopicService.getOne(new LambdaQueryWrapper<PaperTopic>().eq(PaperTopic::getTopicName, newPaperTopic.getTopicName()).last("limit 1")) != null) {
            return;
        }
        newPaperTopic.setIndexName(oftenQuestion.getIndexName());
        newPaperTopic.setPopular(1);
        newPaperTopic.setCreateTime(DateUtil.date());
        paperTopicService.save(newPaperTopic);
        for (PaperTopicItem paperTopicItem : options) {
            PaperTopicItem newPaperTopicItem = new PaperTopicItem();
            BeanUtils.copyProperties(paperTopicItem, newPaperTopicItem);
            newPaperTopicItem.setId(IdWorker.getIdStr());
            newPaperTopicItem.setPaperTopicId(newPaperTopic.getId());
            paperTopicItemService.save(newPaperTopicItem);
        }
    }
}
