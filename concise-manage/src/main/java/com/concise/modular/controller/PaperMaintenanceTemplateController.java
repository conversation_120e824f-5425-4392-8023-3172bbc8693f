package com.concise.modular.controller;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.gen.papermaintenancetemplate.param.PaperMaintenanceTemplateParam;
import com.concise.gen.papermaintenancetemplate.service.PaperMaintenanceTemplateService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 笔录模板控制器
 *
 * <AUTHOR>
 * @date 2025-08-05 09:23:48
 */
@Api(tags = "笔录模板")
@RestController
public class PaperMaintenanceTemplateController {

    @Resource
    private PaperMaintenanceTemplateService paperMaintenanceTemplateService;

    @Resource
    private SysOrgService sysOrgService;

    /**
     * 查询笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    @GetMapping("/paperMaintenanceTemplate/page")
    @ApiOperation("笔录模板_分页查询")
    public ResponseData page(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        return new SuccessResponseData(paperMaintenanceTemplateService.page(paperMaintenanceTemplateParam));
    }

    /**
     * 添加笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    @PostMapping("/paperMaintenanceTemplate/add")
    @ApiOperation("笔录模板_增加")
    @BusinessLog(title = "笔录模板_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        paperMaintenanceTemplateService.add(paperMaintenanceTemplateParam);
        return new SuccessResponseData();
    }

    /**
     * 量卷保存为常用模板
     *
     * @param paperMaintenanceTemplateParam
     * @return
     */
    @PostMapping("/paperMaintenanceTemplate/saveAsTemplate")
    @ApiOperation("笔录模板_保存为常用模板")
    @BusinessLog(title = "笔录模板_保存为常用模板", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData saveAsTemplate(@RequestBody PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        paperMaintenanceTemplateParam.setOperator(LoginContextHolder.me().getSysLoginUser().getName());
        //使用单位设置为当前登录人单位
        String orgId = LoginContextHolder.me().getSysLoginUserOrgId();
        SysOrg sysOrg = sysOrgService.getById(orgId);
        if (sysOrg != null) {
            if (!"sp".equals(sysOrg.getType())) {
                paperMaintenanceTemplateParam.setJzjg(orgId);
                paperMaintenanceTemplateParam.setJzjgName(sysOrg.getName());
            } else {
                //往上找一级
                String parentId = sysOrg.getPid();
                SysOrg parentOrg = sysOrgService.getById(parentId);
                if (parentOrg != null) {
                    paperMaintenanceTemplateParam.setJzjg(parentOrg.getId());
                    paperMaintenanceTemplateParam.setJzjgName(parentOrg.getName());
                }
            }
        }
        paperMaintenanceTemplateService.saveAsTemplate(paperMaintenanceTemplateParam);
        return new SuccessResponseData();
    }

    /**
     * 删除笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    @PostMapping("/paperMaintenanceTemplate/delete")
    @ApiOperation("笔录模板_删除")
    @BusinessLog(title = "笔录模板_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(PaperMaintenanceTemplateParam.delete.class) PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        paperMaintenanceTemplateService.delete(paperMaintenanceTemplateParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    @PostMapping("/paperMaintenanceTemplate/edit")
    @ApiOperation("笔录模板_编辑")
    @BusinessLog(title = "笔录模板_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        paperMaintenanceTemplateService.edit(paperMaintenanceTemplateParam);
        return new SuccessResponseData();
    }

    /**
     * 查看笔录模板
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    @GetMapping("/paperMaintenanceTemplate/detail")
    @ApiOperation("笔录模板_查看")
    public ResponseData detail(@Validated(PaperMaintenanceTemplateParam.detail.class) PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        return new SuccessResponseData(paperMaintenanceTemplateService.detail(paperMaintenanceTemplateParam));
    }

    /**
     * 笔录模板列表
     *
     * <AUTHOR>
     * @date 2025-08-05 09:23:48
     */
    @GetMapping("/paperMaintenanceTemplate/list")
    @ApiOperation("笔录模板_列表")
    public ResponseData list(PaperMaintenanceTemplateParam paperMaintenanceTemplateParam) {
        return new SuccessResponseData(paperMaintenanceTemplateService.list(paperMaintenanceTemplateParam));
    }

}
